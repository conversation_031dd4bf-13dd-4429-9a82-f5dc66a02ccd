/* Base Styles */
:root {
    --primary-color: #8b0000;
    --secondary-color: #333;
    --accent-color: #f8f4e1;
    --text-color: #333;
    --light-color: #fff;
    --dark-color: #222;
    --gray-color: #f4f4f4;
    --border-color: #ddd;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #222;
    position: relative;
}

/* Add steak background to the entire site */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/persianMeatMarket.png');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    opacity: 0.3; /* More visible background */
    z-index: -1;
}

/* RTL Support */
html[dir="rtl"] body {
    font-family: '<PERSON>hom<PERSON>', 'Segoe UI', Geneva, Verdana, sans-serif;
}

html[dir="rtl"] .logo,
html[dir="rtl"] nav ul li,
html[dir="rtl"] .footer-section i {
    margin-right: 0;
    margin-left: 10px;
}

html[dir="rtl"] nav ul li {
    margin-left: 20px;
    margin-right: 0;
}

html[dir="rtl"] .feature i,
html[dir="rtl"] .contact-card i {
    margin-right: 0;
    margin-left: 10px;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: var(--light-color);
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-weight: 600;
    text-align: center;
}

.btn:hover {
    background-color: #6b0000;
}

.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: #444;
}

/* Header */
header {
    background-color: rgba(34, 34, 34, 0.95);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    position: sticky;
    top: 0;
    z-index: 100;
    color: var(--light-color);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 15px;
}

.logo h1 {
    color: var(--light-color);
    font-size: 2rem;
    margin-bottom: 5px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.logo p {
    color: #ddd;
    font-size: 0.9rem;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    color: #ddd;
    font-weight: 600;
    padding: 10px 5px;
    transition: color 0.3s ease, border-color 0.3s ease;
}

nav ul li a:hover,
nav ul li a.active {
    color: var(--light-color);
    border-bottom: 2px solid var(--light-color);
}

/* Language Selector */
.language-selector {
    margin-left: 20px;
}

#language-switcher {
    padding: 5px 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background-color: rgba(51, 51, 51, 0.8);
    color: var(--light-color);
    cursor: pointer;
}

html[dir="rtl"] .language-selector {
    margin-left: 0;
    margin-right: 20px;
}

/* Logo Styling */
.logo {
    display: flex;
    align-items: center;
}

.logo img {
    margin-right: 15px;
}

html[dir="rtl"] .logo img {
    margin-right: 0;
    margin-left: 15px;
}

/* Hero Section */
.hero {
    background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/persianMeatMarket.png');
    background-size: cover;
    background-position: center;
    color: var(--light-color);
    text-align: center;
    padding: 100px 0;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* Features Section */
.features {
    padding: 80px 0;
    background-color: rgba(34, 34, 34, 0.9);
    color: var(--light-color);
    background-image: url('../images/steak-bbq.jpg');
    background-size: cover;
    background-position: center;
    background-blend-mode: overlay;
}

.features .container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.feature {
    text-align: center;
    padding: 30px;
    background-color: rgba(51, 51, 51, 0.8);
    color: var(--light-color);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature:hover {
    transform: translateY(-10px);
}

.feature i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.feature h3 {
    margin-bottom: 15px;
}

/* Featured Products Section */
.featured-products {
    padding: 80px 0;
}

.featured-products h2 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 2rem;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.product {
    background-color: rgba(51, 51, 51, 0.8);
    color: var(--light-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.product:hover {
    transform: translateY(-10px);
}

/* Product Gallery */
.product-gallery {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.product-gallery img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.product-gallery img.active {
    opacity: 1;
}

.gallery-nav {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 5;
}

.gallery-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    margin: 0 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.gallery-dot.active {
    background-color: #fff;
}

.gallery-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 5;
    transition: background-color 0.3s ease;
}

.gallery-arrow:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

.gallery-prev {
    left: 10px;
}

.gallery-next {
    right: 10px;
}

.product h3 {
    padding: 15px 15px 5px;
    font-size: 1.2rem;
}

.product p {
    padding: 0 15px 15px;
    color: #ddd;
}

.featured-products .btn {
    display: block;
    width: 200px;
    margin: 0 auto;
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background-color: rgba(34, 34, 34, 0.85);
    color: var(--light-color);
    text-align: center;
    background-size: cover;
    background-position: center;
    background-blend-mode: overlay;
}

.testimonials h2 {
    margin-bottom: 40px;
    font-size: 2rem;
}

.testimonial {
    max-width: 800px;
    margin: 0 auto;
    padding: 30px;
    background-color: rgba(51, 51, 51, 0.8);
    color: var(--light-color);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.testimonial p {
    font-style: italic;
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.testimonial .customer {
    font-weight: 700;
    color: var(--primary-color);
}

/* Page Header */
.page-header {
    background-image: linear-gradient(rgba(139, 0, 0, 0.85), rgba(139, 0, 0, 0.85)), url('../images/steak-bbq.jpg');
    background-size: cover;
    background-position: center;
    color: var(--light-color);
    text-align: center;
    padding: 60px 0;
}

.page-header h2 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

/* Product Categories */
.product-categories {
    padding: 40px 0;
    text-align: center;
}

.category-nav {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 30px;
}

.category-btn {
    background-color: rgba(51, 51, 51, 0.8);
    color: #ddd;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.category-btn:hover,
.category-btn.active {
    background-color: var(--primary-color);
    color: var(--light-color);
    border-color: var(--primary-color);
}

/* Custom Orders Section */
.custom-orders {
    padding: 80px 0;
    text-align: center;
    background-image: linear-gradient(rgba(248, 244, 225, 0.9), rgba(248, 244, 225, 0.9));
    background-size: cover;
    background-position: center;
}

.custom-orders h2 {
    margin-bottom: 20px;
}

.custom-orders p {
    max-width: 800px;
    margin: 0 auto 30px;
}

/* Contact Info Cards */

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.contact-card {
    text-align: center;
    padding: 30px;
    background-color: rgba(51, 51, 51, 0.8);
    color: var(--light-color);
    border-radius: 8px;
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.contact-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.contact-card h3 {
    margin-bottom: 15px;
}

/* Map Section */
.map {
    padding: 40px 0 80px;
}

.map h2 {
    text-align: center;
    margin-bottom: 30px;
}

.map-container {
    height: 400px;
    background-color: rgba(51, 51, 51, 0.8);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.map-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    color: var(--light-color);
}

/* Contact Section Styling */
.contact-info {
    padding: 80px 0;
    background-color: rgba(34, 34, 34, 0.85);
    color: var(--light-color);
    background-size: cover;
    background-position: center;
    background-blend-mode: overlay;
}

.contact-form h2 {
    text-align: center;
    margin-bottom: 40px;
}

form {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--light-color);
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

input,
textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
}

textarea {
    resize: vertical;
}

form .btn {
    margin-top: 10px;
}

/* Footer */
footer {
    background-color: rgba(17, 17, 17, 0.95);
    color: var(--light-color);
    padding: 60px 0 20px;
    background-size: cover;
    background-position: center;
    background-blend-mode: overlay;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.footer-section h3 {
    color: var(--light-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.footer-section p {
    margin-bottom: 10px;
    color: #bbb;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #bbb;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--light-color);
}

.footer-section i {
    margin-right: 10px;
    color: var(--primary-color);
}

.copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #444;
    color: #bbb;
}

/* Responsive Design */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }

    nav ul {
        margin-top: 20px;
    }

    nav ul li {
        margin: 0 10px;
    }

    .hero {
        padding: 60px 0;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .feature {
        padding: 20px;
    }

    .contact-form form {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav ul li {
        margin: 5px 0;
    }

    .hero h2 {
        font-size: 1.8rem;
    }

    .product-grid {
        grid-template-columns: 1fr;
    }

    .contact-grid {
        grid-template-columns: 1fr;
    }
}
