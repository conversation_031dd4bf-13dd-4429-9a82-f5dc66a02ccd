// Translations for the Meatland website
const translations = {
    en: {
        // Navigation
        "home": "Home",
        "products": "Our Products",
        "contact": "Contact Us",
        "language": "Language",

        // Header
        "tagline": "Premium Butcher Shop",

        // Hero Section
        "hero_title": "Quality Meats for Every Occasion",
        "hero_subtitle": "Serving the community with premium cuts and exceptional service since 2023",
        "explore_products": "Explore Our Products",

        // Features
        "premium_cuts": "Premium Cuts",
        "premium_cuts_desc": "Hand-selected quality meats from trusted sources",
        "locally_sourced": "Locally Sourced",
        "locally_sourced_desc": "Supporting local farmers and sustainable practices",
        "expert_advice": "Expert Advice",
        "expert_advice_desc": "Our butchers are here to help with cooking tips",

        // Featured Products
        "featured_products": "Featured Products",
        "view_all_products": "View All Products",

        // Product Names
        "ribeye": "Prime Ribeye Steak",
        "ribeye_desc": "Premium cut with excellent marbling",
        "chicken": "Organic Chicken Breast",
        "chicken_desc": "Free-range, hormone-free chicken",
        "lamb": "Lamb Chops",
        "lamb_desc": "Tender and flavorful premium lamb",
        "filet_mignon": "Filet Mignon",
        "filet_mignon_desc": "Tender and lean premium cut",
        "ny_strip": "New York Strip",
        "ny_strip_desc": "Well-marbled with a robust flavor",
        "ground_beef": "Ground Beef",
        "ground_beef_desc": "80/20 premium ground beef",
        "whole_chicken": "Whole Chicken",
        "whole_chicken_desc": "Farm-raised, all-natural",
        "turkey": "Turkey Breast",
        "turkey_desc": "Lean and flavorful",
        "pork_chops": "Pork Chops",
        "pork_chops_desc": "Center-cut, bone-in",
        "bacon": "Bacon",
        "bacon_desc": "Thick-cut, hickory smoked",
        "leg_of_lamb": "Leg of Lamb",
        "leg_of_lamb_desc": "Perfect for roasting",
        "dry_aged": "Dry-Aged Ribeye",
        "dry_aged_desc": "Aged 30 days for exceptional flavor",
        "wagyu": "Wagyu Beef",
        "wagyu_desc": "Premium Japanese-style beef",
        "stew_meat": "Stew Meat",
        "stew_meat_desc": "Perfect for slow cooking and stews",
        "lamb_shank": "Lamb Shank",
        "lamb_shank_desc": "Rich and flavorful, perfect for braising",

        // Prices
        "price_per_lb": "$$/lb",

        // Testimonials
        "testimonials": "What Our Customers Say",
        "testimonial_1": "The quality of meat at Meatland is unmatched. Their ribeye steaks are the best I've ever had!",
        "customer_1": "- John D.",

        // Products Page
        "our_products": "Our Products",
        "products_subtitle": "Quality meats for every occasion",
        "all_products": "All Products",
        "beef": "Beef",
        "poultry": "Poultry",
        "pork": "Pork",
        "lamb_category": "Lamb",
        "specialty": "Specialty",

        // Custom Orders
        "custom_orders": "Custom Orders",
        "custom_orders_desc": "Don't see what you're looking for? We offer custom cuts and special orders. Contact us for more information.",

        // Contact Page
        "contact_us": "Contact Us",
        "contact_subtitle": "We'd love to hear from you",
        "our_location": "Our Location",
        "phone": "Phone",
        "email": "Email",
        "hours": "Hours",
        "find_us": "Find Us",
        "send_message": "Send Us a Message",
        "name": "Name",
        "message": "Message",
        "send": "Send Message",

        // Footer
        "quick_links": "Quick Links",
        "copyright": "© 2023 Meatland. All rights reserved."
    },
    fa: {
        // Navigation
        "home": "خانه",
        "products": "محصولات ما",
        "contact": "تماس با ما",
        "language": "زبان",

        // Header
        "tagline": "قصابی ممتاز",

        // Hero Section
        "hero_title": "گوشت با کیفیت برای هر مناسبت",
        "hero_subtitle": "ارائه خدمات به جامعه با برش‌های ممتاز و خدمات استثنایی از سال 2023",
        "explore_products": "مشاهده محصولات ما",

        // Features
        "premium_cuts": "برش‌های ممتاز",
        "premium_cuts_desc": "گوشت‌های با کیفیت دست‌چین شده از منابع مورد اعتماد",
        "locally_sourced": "تهیه شده از منابع محلی",
        "locally_sourced_desc": "حمایت از کشاورزان محلی و شیوه‌های پایدار",
        "expert_advice": "مشاوره تخصصی",
        "expert_advice_desc": "قصاب‌های ما برای کمک به نکات پخت و پز اینجا هستند",

        // Featured Products
        "featured_products": "محصولات ویژه",
        "view_all_products": "مشاهده همه محصولات",

        // Product Names
        "ribeye": "استیک ریب‌آی ممتاز",
        "ribeye_desc": "برش ممتاز با رگه‌های چربی عالی",
        "chicken": "سینه مرغ ارگانیک",
        "chicken_desc": "مرغ آزاد و بدون هورمون",
        "lamb": "دنده بره",
        "lamb_desc": "گوشت بره نرم و خوش طعم",
        "filet_mignon": "فیله مینیون",
        "filet_mignon_desc": "برش نرم و کم چربی ممتاز",
        "ny_strip": "استیک نیویورکی",
        "ny_strip_desc": "با رگه‌های چربی و طعم قوی",
        "ground_beef": "گوشت چرخ کرده",
        "ground_beef_desc": "گوشت چرخ کرده ممتاز ۸۰/۲۰",
        "whole_chicken": "مرغ کامل",
        "whole_chicken_desc": "پرورش یافته در مزرعه، کاملاً طبیعی",
        "turkey": "سینه بوقلمون",
        "turkey_desc": "کم چرب و خوش طعم",
        "pork_chops": "چاپ خوک",
        "pork_chops_desc": "برش مرکزی، با استخوان",
        "bacon": "بیکن",
        "bacon_desc": "برش ضخیم، دودی گردو",
        "leg_of_lamb": "ران بره",
        "leg_of_lamb_desc": "مناسب برای کباب کردن",
        "dry_aged": "ریب‌آی خشک شده",
        "dry_aged_desc": "۳۰ روز خشک شده برای طعم استثنایی",
        "wagyu": "گوشت واگیو",
        "wagyu_desc": "گوشت ممتاز به سبک ژاپنی",
        "stew_meat": "گوشت خورشتی",
        "stew_meat_desc": "مناسب برای پخت آهسته و خورشت",
        "lamb_shank": "ساق بره",
        "lamb_shank_desc": "غنی و خوش طعم، مناسب برای بریزینگ",

        // Prices
        "price_per_lb": "تومان/کیلو",

        // Testimonials
        "testimonials": "نظرات مشتریان ما",
        "testimonial_1": "کیفیت گوشت در میت‌لند بی‌نظیر است. استیک‌های ریب‌آی آنها بهترین استیکی است که تا به حال خورده‌ام!",
        "customer_1": "- جان د.",

        // Products Page
        "our_products": "محصولات ما",
        "products_subtitle": "گوشت با کیفیت برای هر مناسبت",
        "all_products": "همه محصولات",
        "beef": "گوشت گاو",
        "poultry": "مرغ",
        "pork": "گوشت خوک",
        "lamb_category": "گوشت بره",
        "specialty": "تخصصی",

        // Custom Orders
        "custom_orders": "سفارشات سفارشی",
        "custom_orders_desc": "آنچه را که به دنبال آن هستید نمی‌بینید؟ ما برش‌های سفارشی و سفارشات ویژه ارائه می‌دهیم. برای اطلاعات بیشتر با ما تماس بگیرید.",

        // Contact Page
        "contact_us": "تماس با ما",
        "contact_subtitle": "ما دوست داریم از شما بشنویم",
        "our_location": "مکان ما",
        "phone": "تلفن",
        "email": "ایمیل",
        "hours": "ساعات کاری",
        "find_us": "ما را پیدا کنید",
        "send_message": "برای ما پیام بفرستید",
        "name": "نام",
        "message": "پیام",
        "send": "ارسال پیام",

        // Footer
        "quick_links": "لینک‌های سریع",
        "copyright": "© 2023 میت‌لند. تمامی حقوق محفوظ است."
    }
};

// Default language
let currentLanguage = 'en';

// Function to change the language
function changeLanguage(lang) {
    if (translations[lang]) {
        currentLanguage = lang;
        document.documentElement.lang = lang;
        document.documentElement.dir = lang === 'fa' ? 'rtl' : 'ltr';
        updateContent();

        // Save language preference
        localStorage.setItem('meatland-language', lang);
    }
}

// Function to update content based on selected language
function updateContent() {
    const elements = document.querySelectorAll('[data-i18n]');
    elements.forEach(element => {
        const key = element.getAttribute('data-i18n');
        if (translations[currentLanguage][key]) {
            element.textContent = translations[currentLanguage][key];
        }
    });

    // Update placeholder attributes
    const placeholders = document.querySelectorAll('[data-i18n-placeholder]');
    placeholders.forEach(element => {
        const key = element.getAttribute('data-i18n-placeholder');
        if (translations[currentLanguage][key]) {
            element.placeholder = translations[currentLanguage][key];
        }
    });
}

// Initialize language based on saved preference or browser language
document.addEventListener('DOMContentLoaded', function() {
    // Check for saved language preference
    const savedLanguage = localStorage.getItem('meatland-language');

    if (savedLanguage && translations[savedLanguage]) {
        currentLanguage = savedLanguage;
    } else {
        // Check browser language
        const browserLang = navigator.language || navigator.userLanguage;
        if (browserLang.startsWith('fa') && translations['fa']) {
            currentLanguage = 'fa';
        }
    }

    // Set initial language
    document.documentElement.lang = currentLanguage;
    document.documentElement.dir = currentLanguage === 'fa' ? 'rtl' : 'ltr';

    // Update language switcher
    const languageSwitcher = document.getElementById('language-switcher');
    if (languageSwitcher) {
        languageSwitcher.value = currentLanguage;
    }

    // Update content
    updateContent();
});
